<?php

require_once 'CityMapping.php';

class ApiPriceClient {
    private $baseUrl = 'http://oxford.kibernetika.co.rs/api/ExportSaParametrom2';
    private $apiKey = 'QVXsMTv+DSljBlWCDIHBiiTfqJuMCo4y/uloErmMOlA=';
    private $cacheDir;
    private $cacheExpiry = 86400; // 24 hours in seconds

    public function __construct($cacheDir = null) {
        $this->cacheDir = $cacheDir ?: __DIR__ . '/cache/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    public function getCityPrices($cityApiId, $courseTypes = null, $language = 'engleski') {
        if ($courseTypes === null) {
            $courseTypes = array_values(CityMapping::getAllCourseTypes());
        }

        $cacheKey = $this->getCacheKey($cityApiId, $language);
        $cachedData = $this->getCachedData($cacheKey);
        
        if ($cachedData !== null) {
            return $cachedData;
        }

        $allPrices = [];
        foreach ($courseTypes as $courseType) {
            $url = $this->buildApiUrl($cityApiId, $courseType, $language);
            $data = $this->fetchFromApi($url);
            if ($data) {
                $allPrices = array_merge($allPrices, $data);
            }
        }

        $result = [
            'cityApiId' => $cityApiId,
            'cityName' => CityMapping::getCityNameByApiId($cityApiId),
            'language' => $language,
            'lastUpdated' => date('Y-m-d H:i:s'),
            'kursevi' => $allPrices
        ];

        $this->cacheData($cacheKey, $result);
        return $result;
    }

    public function getAllCitiesPrices($language = 'engleski') {
        $allCities = CityMapping::getAllCities();
        $allPrices = [];

        foreach ($allCities as $cityKey => $cityData) {
            $cityPrices = $this->getCityPrices($cityData['apiId'], null, $language);
            $allPrices[$cityKey] = $cityPrices;
        }

        return $allPrices;
    }

    public function refreshAllCaches($language = 'engleski') {
        $allCities = CityMapping::getAllCities();
        $refreshed = [];
        $errors = [];

        foreach ($allCities as $cityKey => $cityData) {
            try {
                // Force refresh by clearing cache first
                $cacheKey = $this->getCacheKey($cityData['apiId'], $language);
                $this->clearCache($cacheKey);
                
                $cityPrices = $this->getCityPrices($cityData['apiId'], null, $language);
                $refreshed[] = $cityKey;
            } catch (Exception $e) {
                $errors[$cityKey] = $e->getMessage();
            }
        }

        return [
            'refreshed' => $refreshed,
            'errors' => $errors,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    public function buildApiUrl($cityApiId, $courseType, $language) {
        // Correct format: parametri=105,2,'engleski',2,1
        $params = "{$cityApiId},{$courseType},'{$language}',2,1";
        return "{$this->baseUrl}?izvestaj=Usluge_Kurseva_2&parametri={$params}";
    }

    public function fetchFromApi($url) {
        // Use Token header as shown in cURL
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'header' => "Token: {$this->apiKey}"
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            error_log("Failed to fetch data from API: {$url}. Error: " . (isset($error['message']) ? $error['message'] : 'Unknown error'));
            return null;
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Invalid JSON response from API: {$url}. JSON Error: " . json_last_error_msg());
            return null;
        }

        return $data;
    }

    private function getCacheKey($cityApiId, $language) {
        return "city_{$cityApiId}_{$language}";
    }

    private function getCacheFilePath($cacheKey) {
        return $this->cacheDir . $cacheKey . '.json';
    }

    private function getCachedData($cacheKey) {
        $filePath = $this->getCacheFilePath($cacheKey);
        
        if (!file_exists($filePath)) {
            return null;
        }

        $fileTime = filemtime($filePath);
        if (time() - $fileTime > $this->cacheExpiry) {
            unlink($filePath);
            return null;
        }

        $content = file_get_contents($filePath);
        return json_decode($content, true);
    }

    private function cacheData($cacheKey, $data) {
        $filePath = $this->getCacheFilePath($cacheKey);
        file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    private function clearCache($cacheKey) {
        $filePath = $this->getCacheFilePath($cacheKey);
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    public function isCacheExpired($cityApiId, $language) {
        $cacheKey = $this->getCacheKey($cityApiId, $language);
        $filePath = $this->getCacheFilePath($cacheKey);
        
        if (!file_exists($filePath)) {
            return true;
        }

        $fileTime = filemtime($filePath);
        return (time() - $fileTime > $this->cacheExpiry);
    }

    public function getCacheStatus() {
        $allCities = CityMapping::getAllCities();
        $status = [];

        foreach ($allCities as $cityKey => $cityData) {
            $cacheKey = $this->getCacheKey($cityData['apiId'], 'engleski');
            $filePath = $this->getCacheFilePath($cacheKey);
            
            if (file_exists($filePath)) {
                $fileTime = filemtime($filePath);
                $isExpired = (time() - $fileTime > $this->cacheExpiry);
                $status[$cityKey] = [
                    'exists' => true,
                    'lastUpdated' => date('Y-m-d H:i:s', $fileTime),
                    'expired' => $isExpired,
                    'ageHours' => round((time() - $fileTime) / 3600, 1)
                ];
            } else {
                $status[$cityKey] = [
                    'exists' => false,
                    'expired' => true
                ];
            }
        }

        return $status;
    }
}
