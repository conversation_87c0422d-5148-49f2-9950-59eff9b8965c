<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cenovnik kurseva španskog jezika - Akademija Oxford</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            max-width: 1200px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin: 0 0 30px 0;
            border-radius: 0;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .left__table {
            position: relative;
            z-index: 10;
            background: white;
            border-radius: 8px 0 0 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .right__table {
            overflow-x: auto;
            background: white;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .scroll__me {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table th {
            background: linear-gradient(135deg, #f38630 0%, #e8743b 100%);
            color: white;
            text-align: center;
            vertical-align: middle;
            font-weight: 600;
            padding: 15px 8px;
            border: none;
            font-size: 0.9em;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .table td {
            text-align: center;
            vertical-align: middle;
            padding: 12px 8px;
            border: 1px solid #e9ecef;
            font-size: 0.9em;
            transition: background-color 0.2s ease;
        }
        
        .table tbody tr:hover td {
            background-color: #f8f9fa;
        }
        
        .left__table .table td {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            border: none;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        
        .price-cell {
            font-weight: 600;
            color: #2c5aa0;
        }
        
        .price-monthly {
            font-size: 0.8em;
            color: #666;
            font-weight: normal;
        }
        
        .no-price {
            color: #dc3545;
            font-size: 1.2em;
        }
        
        .info-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .cache-info {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 20px;
        }
        
        .legend {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .legend h5 {
            margin-top: 0;
            color: #495057;
        }
        
        .legend ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
        
        .legend li {
            margin-bottom: 5px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <div class="container">
            <h1>Cenovnik kurseva engleskog jezika</h1>
            <p>Akademija Oxford - Cene za sve poslovnice</p>
        </div>
    </div>

    <div class="container">
        <!-- <div class="info-section">
            <div class="row">
                <div class="col-md-8">
                    <h4>Aktuelne cene kurseva</h4>
                    <p>Cene se automatski ažuriraju iz centralnog sistema i cache-uju za optimalne performanse. Poslednje ažuriranje podataka je izvršeno u poslednjih 24 sata.</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="refresh_prices.php" class="btn btn-custom">
                        <i class="glyphicon glyphicon-refresh"></i> Osveži cene
                    </a>
                    <a href="cache_status.php" class="btn btn-info" style="margin-left: 10px;">
                        <i class="glyphicon glyphicon-info-sign"></i> Status cache-a
                    </a>
                </div>
            </div>
        </div> -->
        
        <div class="row">
            <div class="col-md-12">
                <?php
                require_once 'inc/cene/ApiCenovnikTabela.php';
                
                // Set language variable for the pricing table
                $thisLang = 'engleski';
                
                try {
                    // Prikaži samo realne tipove kurseva koji postoje u podacima za A1 nivo
                    // Jezik će automatski biti uzet iz $thisLang promenljive
                    ApiCenovnikTabela::prikaziTabelu();
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">';
                    echo '<strong>Greška pri učitavanju cena:</strong> ' . htmlspecialchars($e->getMessage());
                    echo '</div>';
                    echo '<div class="alert alert-info">';
                    echo '<p>Molimo pokušajte sledeće:</p>';
                    echo '<ul>';
                    echo '<li><a href="refresh_prices.php">Osvežite cene</a> - ovo će ponovo učitati podatke sa API-ja</li>';
                    echo '<li><a href="cache_status.php">Proverite status cache-a</a> - da vidite da li su podaci dostupni</li>';
                    echo '<li>Kontaktirajte administratora ako problem i dalje postoji</li>';
                    echo '</ul>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <!-- <div class="legend">
            <h5>Objašnjenje tipova kurseva:</h5>
            <ul>
                <li><strong>GRUPNA mesečno:</strong> Standardni grupni kursevi (7-20 polaznika), cena je mesečna</li>
                <li><strong>INDIVIDUALNA:</strong> Privatni časovi jedan-na-jedan sa profesorom</li>
                <li><strong>ONLINE:</strong> Grupni kursevi koji se održavaju online putem video poziva</li>
            </ul>
            <p><strong>Napomena:</strong> Simbol ☎ označava da je potrebno kontaktirati poslovnicu za informacije o ceni.</p>
        </div> -->
        
        <!-- <div class="cache-info text-center">
            <small>
                Sistem automatski cache-uje podatke radi bržeg učitavanja. 
                Cache se osveža svakih 24 sata ili na zahtev.
            </small>
        </div> -->
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
</body>
</html>
