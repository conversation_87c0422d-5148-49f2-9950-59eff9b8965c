<?php

require_once 'ApiPriceClient.php';
require_once 'CityMapping.php';

class ApiCenovnikTabela {
    private static $bezCeneSimbol = '☎'; // Phone symbol for missing prices

    public static function prikaziTabelu($jezik = null, $vrsteKurseva = ['grupa_4_plus', 'grupni_online', 'individualni', 'individualni_online', 'mikro_grupa'], $nivo = 'A1') {
        // Use global $thisLang if available, otherwise use parameter or default
        if ($jezik === null) {
            global $thisLang;
            $jezik = isset($thisLang) ? $thisLang : 'engleski';
        }
        
        $apiClient = new ApiPriceClient();
        $allPrices = $apiClient->getAllCitiesPrices($jezik);
        
        self::generisiTabelu($allPrices, $vrsteKurseva, $nivo, $jezik);
    }

    private static function generisiTabelu($allPrices, $vrsteKurseva, $nivo, $jezik) {
        echo '
        <div class="row" id="cene">
          <div class="col-xs-4 col-sm-3 left__table">
              <table class="table">
                <thead>
                  <tr>
                    <th>&nbsp;</th>
                  </tr>
                </thead>
                <tbody>';
        
        self::kolonaOblici($vrsteKurseva);
        
        echo '
                </tbody>
              </table>
          </div>
          <div class="col-xs-8 col-sm-9 right__table">
            <div class="table-responsive scroll__me">
              <table class="table">
                <thead>
                  <tr>';
        
        self::zaglavljePoslovnice();
        
        echo '
                  </tr>
                </thead>
                <tbody>';
        
        self::redCenaOblika($allPrices, $vrsteKurseva, $nivo, $jezik);
        
        echo '
                </tbody>
              </table>
            </div>
          </div>
        </div>';
    }

    private static function kolonaOblici($vrsteKurseva) {
        $opisOblika = [
            'grupa_4_plus' => 'GRUPNA 4+ polaznika',
            'grupni_online' => 'GRUPNA - ONLINE',
            'individualni' => 'INDIVIDUALNA', 
            'individualni_online' => 'INDIVIDUALNA - ONLINE',
            'mikro_grupa' => 'MIKRO GRUPA'
        ];

        foreach ($vrsteKurseva as $vrsta) {
            $opis = isset($opisOblika[$vrsta]) ? $opisOblika[$vrsta] : strtoupper(str_replace('_', ' ', $vrsta));
            echo '<tr>';
            echo '<td>' . $opis . '</td>';
            echo '</tr>';
        }
    }

    private static function zaglavljePoslovnice() {
        $cities = CityMapping::getAllCities();
        foreach ($cities as $cityData) {
            echo '<th>' . $cityData['naziv'] . '</th>';
        }
    }

    private static function redCenaOblika($allPrices, $vrsteKurseva, $nivo, $jezik) {
        $cities = CityMapping::getAllCities();
        
        foreach ($vrsteKurseva as $vrstaKursa) {
            echo '<tr>';
            
            foreach ($cities as $cityKey => $cityData) {
                $cena = self::getCenaForCityAndType($allPrices, $cityKey, $vrstaKursa, $nivo, $jezik);
                
                if ($cena !== null && $cena > 0) {
                    $formattedCena = number_format($cena, 0, ',', '.') . ' RSD';
                    if ($vrstaKursa === 'grupna_mesecno') {
                        echo '<td class="price-cell">' . $formattedCena . '<br><span class="price-monthly">mesečno</span></td>';
                    } else {
                        echo '<td class="price-cell">' . $formattedCena . '</td>';
                    }
                } else {
                    echo '<td class="no-price">' . self::$bezCeneSimbol . '</td>';
                }
            }
            
            echo '</tr>';
        }
    }

    private static function getCenaForCityAndType($allPrices, $cityKey, $vrstaKursa, $nivo = 'A1', $jezik = 'engleski') {
        if (!isset($allPrices[$cityKey]) || !isset($allPrices[$cityKey]['kursevi'])) {
            return null;
        }

        $kursevi = $allPrices[$cityKey]['kursevi'];
        $courseTypeId = CityMapping::getCourseTypeId($vrstaKursa);

        // Find course that matches exact course name, type and level with valid price
        foreach ($kursevi as $kurs) {
            $exactMatch = self::matchesExactCourse($kurs, $vrstaKursa, $nivo, $cityKey, $jezik);
            $typeMatch = self::matchesCourseType($kurs, $vrstaKursa, $courseTypeId);
            $levelMatch = self::matchesLevel($kurs, $nivo);



            if ($exactMatch && $typeMatch && $levelMatch) {
                $cena = isset($kurs['Kcpp_Cena']) ? $kurs['Kcpp_Cena'] : null;
                // Skip courses with price 0 and continue looking for valid price
                if ($cena !== null && $cena > 0) {
                    return $cena;
                }
            }
        }

        return null;
    }

    private static function matchesExactCourse($kurs, $vrstaKursa, $nivo, $cityKey, $jezik) {
        $kursNaziv = isset($kurs['Kurs_Naziv']) ? $kurs['Kurs_Naziv'] : '';

        // Language mapping for course names - all 39 languages
        $languageNames = [
            'engleski' => 'Engleski jezik',
            'nemački' => 'Nemački jezik',
            'italijanski' => 'Italijanski jezik',
            'španski' => 'Španski jezik',
            'francuski' => 'Francuski jezik',
            'norveški' => 'Norveški jezik',
            'švedski' => 'Švedski jezik',
            'ruski' => 'Ruski jezik',
            'grčki' => 'Grčki jezik',
            'turski' => 'Turski jezik',
            'japanski' => 'Japanski jezik',
            'kineski' => 'Kineski jezik',
            'arapski' => 'Arapski jezik',
            'albanski' => 'Albanski jezik',
            'bugarski' => 'Bugarski jezik',
            'bosanski' => 'Bosanski jezik',
            'češki' => 'Češki jezik',
            'danski' => 'Danski jezik',
            'estonski' => 'Estonski jezik',
            'hebrejski' => 'Hebrejski jezik',
            'holandski' => 'Holandski jezik',
            'hrvatski' => 'Hrvatski jezik',
            'korejski' => 'Korejski jezik',
            'mađarski' => 'Mađarski jezik',
            'makedonski' => 'Makedonski jezik',
            'poljski' => 'Poljski jezik',
            'portugalski' => 'Portugalski jezik',
            'rumunski' => 'Rumunski jezik',
            'slovački' => 'Slovački jezik',
            'slovenački' => 'Slovenački jezik',
            'srpski' => 'Srpski jezik',
            'ukrajinski' => 'Ukrajinski jezik',
            'finski' => 'Finski jezik',
            'latinski' => 'Latinski jezik',
            'znakovni' => 'Znakovni jezik',
            'bunjevački' => 'Bunjevački jezik',
            'crnogorski' => 'Crnogorski jezik',
            'litvanski' => 'Litvanski jezik',
            'rusinski' => 'Rusinski jezik'
        ];

        // Expected course name format: "Engleski jezik - A1"
        $expectedName = isset($languageNames[$jezik]) ? $languageNames[$jezik] . ' - ' . $nivo : '';

        return $kursNaziv === $expectedName;
    }

    private static function matchesCourseType($kurs, $vrstaKursa, $courseTypeId) {
        $vrsta = strtolower(isset($kurs['Vrsta']) ? $kurs['Vrsta'] : '');
        $vkNaziv = strtolower(isset($kurs['Vk_Naziv']) ? $kurs['Vk_Naziv'] : '');



        switch ($vrstaKursa) {
            case 'grupa_4_plus':
                return ($vrsta === 'grupa 4+' || $vkNaziv === 'grupa 4+');
            case 'grupni_online':
                return ($vrsta === 'grupni - online' || $vkNaziv === 'grupni - online' ||
                        $vrsta === 'grupa 4+ - online' || $vkNaziv === 'grupa 4+ - online' ||
                        $vrsta === 'grupa 4+  - online' || $vkNaziv === 'grupa 4+  - online');
            case 'individualni':
                return ($vrsta === 'individualni' || $vkNaziv === 'individualni');
            case 'individualni_online':
                return ($vrsta === 'individualni - online' || $vkNaziv === 'individualni - online');
            case 'mikro_grupa':
                return ($vrsta === 'mikro grupa' || $vkNaziv === 'mikro grupa');
            default:
                return false;
        }
    }

    private static function matchesLevel($kurs, $targetLevel) {
        $kursNivo = isset($kurs['Nivo']) ? $kurs['Nivo'] : '';
        return $kursNivo === $targetLevel;
    }

    public static function prikaziCacheStatus() {
        $apiClient = new ApiPriceClient();
        $status = $apiClient->getCacheStatus();
        
        echo '<div class="cache-status">';
        echo '<h3>Cache Status</h3>';
        echo '<table class="table table-striped">';
        echo '<thead><tr><th>Grad</th><th>Cache Status</th><th>Poslednje ažuriranje</th><th>Starost (sati)</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($status as $cityKey => $cityStatus) {
            $cities = CityMapping::getAllCities();
            $cityName = isset($cities[$cityKey]['naziv']) ? $cities[$cityKey]['naziv'] : $cityKey;
            
            echo '<tr>';
            echo '<td>' . $cityName . '</td>';
            
            if ($cityStatus['exists']) {
                $statusClass = $cityStatus['expired'] ? 'danger' : 'success';
                $statusText = $cityStatus['expired'] ? 'Expired' : 'Valid';
                echo '<td><span class="label label-' . $statusClass . '">' . $statusText . '</span></td>';
                echo '<td>' . $cityStatus['lastUpdated'] . '</td>';
                echo '<td>' . $cityStatus['ageHours'] . '</td>';
            } else {
                echo '<td><span class="label label-warning">Missing</span></td>';
                echo '<td>-</td>';
                echo '<td>-</td>';
            }
            
            echo '</tr>';
        }
        
        echo '</tbody></table>';
        echo '</div>';
    }
}
