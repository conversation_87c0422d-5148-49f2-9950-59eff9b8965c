<?php

require_once 'inc/cene/ApiPriceClient.php';
require_once 'inc/cene/CityMapping.php';

// Set time limit for long-running script
set_time_limit(0); // No time limit

// Start timing
$startTime = microtime(true);

// Language mapping from display names to API parameter names
$languageMapping = [
    'Kurs engleskog jezika' => 'engleski',
    'Nemački jezik - Kurs nemačkog jezika' => 'nemački',
    'Italijanski jezik - Kurs italijanskog jezika' => 'italijanski',
    'Španski jezik - Kurs španskog jezika' => 'španski',
    'Francuski jezik - Kurs francuskog jezika' => 'francuski',
    'Norveški jezik - Kurs norveškog jezika' => 'norveški',
    'Švedski jezik - Kurs švedskog jezika' => 'švedski',
    'Ruski jezik - Kurs ruskog jezika' => 'ruski',
    'Gr<PERSON><PERSON> jezik - Kurs grčkog jezika' => 'gr<PERSON>ki',
    'Turski jezik - Kurs turskog jezika' => 'turski',
    'Japanski jezik - Kurs japanskog jezika' => 'japanski',
    'Kineski jezik - Kurs kineskog jezika' => 'kineski',
    'Arapski jezik - Kurs arapskog jezika' => 'arapski',
    'Albanski jezik - Kurs albanskog jezika' => 'albanski',
    'Bugarski jezik - Kurs bugarskog jezika' => 'bugarski',
    'Bosanski jezik - Kurs bosanskog jezika' => 'bosanski',
    'Češki jezik - Kurs češkog jezika' => 'češki',
    'Danski jezik - Kurs danskog jezika' => 'danski',
    'Estonski jezik - Kurs estonskog jezika' => 'estonski',
    'Hebrejski jezik - Kurs hebrejskog jezika' => 'hebrejski',
    'Holandski jezik - Kurs holandskog jezika' => 'holandski',
    'Hrvatski jezik - Kurs hrvatskog jezika' => 'hrvatski',
    'Korejski jezik - Kurs korejskog jezika' => 'korejski',
    'Madjarski jezik - Kurs mađarskog jezika' => 'mađarski',
    'Makedonski jezik - Kurs makedonskog jezika' => 'makedonski',
    'Poljski jezik - Kurs poljskog jezika' => 'poljski',
    'Portugalski jezik - Kurs portugalskog jezika' => 'portugalski',
    'Rumunski jezik - Kurs rumunskog jezika' => 'rumunski',
    'Slovački jezik - Kurs slovačkog jezika' => 'slovački',
    'Slovenački jezik - Kurs slovenačkog jezika' => 'slovenački',
    'Srpski jezik za strance - Kurs srpskog jezika za strance' => 'srpski',
    'Ukrajinski jezik - Kurs ukrajinskog jezika' => 'ukrajinski',
    'Finski jezik - Kurs finskog jezika' => 'finski',
    'Latinski jezik - Kurs latinskog jezika' => 'latinski',
    'Znakovni jezik - Kurs znakovnog jezika' => 'znakovni',
    'Bunjevački jezik - Kurs bunjevačkog jezika' => 'bunjevački',
    'Crnogorski jezik - Kurs crnogorskog jezika' => 'crnogorski',
    'Litvanski jezik - Kurs litvanskog jezika' => 'litvanski',
    'Rusinski jezik - Kurs rusinskog jezika' => 'rusinski'
];

// Log file for tracking operations
$logFile = __DIR__ . '/logs/language_generation.log';
$logDir = dirname($logFile);

if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND | LOCK_EX);
    echo "[{$timestamp}] {$message}\n";
}

function compareDataWithCache($newData, $cacheFilePath) {
    if (!file_exists($cacheFilePath)) {
        return false; // No cache exists, data is different
    }
    
    $cachedContent = file_get_contents($cacheFilePath);
    $cachedData = json_decode($cachedContent, true);
    
    if (!$cachedData) {
        return false; // Invalid cache, treat as different
    }
    
    // Compare only the course data, ignore timestamps
    $newCourses = isset($newData['kursevi']) ? $newData['kursevi'] : [];
    $cachedCourses = isset($cachedData['kursevi']) ? $cachedData['kursevi'] : [];
    
    return json_encode($newCourses) === json_encode($cachedCourses);
}

function generateLanguageCachesWithComparison($languages) {
    $apiClient = new ApiPriceClient();
    $totalLanguages = count($languages);
    $processedLanguages = 0;
    $totalErrors = 0;
    $totalUpdated = 0;
    $totalSkipped = 0;

    logMessage("Starting smart cache generation for {$totalLanguages} languages");

    foreach ($languages as $displayName => $apiName) {
        $processedLanguages++;
        logMessage("Processing language {$processedLanguages}/{$totalLanguages}: {$displayName} ({$apiName})");
        
        $languageUpdated = 0;
        $languageSkipped = 0;
        $languageErrors = 0;
        
        try {
            $allCities = CityMapping::getAllCities();
            
            foreach ($allCities as $cityKey => $cityData) {
                try {
                    // Get fresh data from API without using cache
                    $courseTypes = array_values(CityMapping::getAllCourseTypes());
                    $allPrices = [];
                    
                    foreach ($courseTypes as $courseType) {
                        $url = $apiClient->buildApiUrl($cityData['apiId'], $courseType, $apiName);
                        $data = $apiClient->fetchFromApi($url);
                        if ($data) {
                            $allPrices = array_merge($allPrices, $data);
                        }
                    }
                    
                    $newData = [
                        'cityApiId' => $cityData['apiId'],
                        'cityName' => $cityKey,
                        'language' => $apiName,
                        'lastUpdated' => date('Y-m-d H:i:s'),
                        'kursevi' => $allPrices
                    ];
                    
                    // Check if data has changed
                    $cacheKey = "city_{$cityData['apiId']}_{$apiName}";
                    $cacheFilePath = __DIR__ . "/inc/cene/cache/{$cacheKey}.json";
                    
                    if (compareDataWithCache($newData, $cacheFilePath)) {
                        $languageSkipped++;
                        // Update only timestamp in existing cache
                        if (file_exists($cacheFilePath)) {
                            $existingData = json_decode(file_get_contents($cacheFilePath), true);
                            $existingData['lastUpdated'] = date('Y-m-d H:i:s');
                            file_put_contents($cacheFilePath, json_encode($existingData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        // Data has changed, update cache
                        file_put_contents($cacheFilePath, json_encode($newData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                        $languageUpdated++;
                    }
                    
                } catch (Exception $e) {
                    logMessage("Error processing {$cityKey} for {$apiName}: " . $e->getMessage());
                    $languageErrors++;
                }
            }
            
            logMessage("Language {$apiName}: {$languageUpdated} updated, {$languageSkipped} skipped, {$languageErrors} errors");
            $totalUpdated += $languageUpdated;
            $totalSkipped += $languageSkipped;
            $totalErrors += $languageErrors;
            
            // Small delay to avoid overwhelming the API
            sleep(1);
            
        } catch (Exception $e) {
            logMessage("Exception for language {$apiName}: " . $e->getMessage());
            $totalErrors++;
        }
    }

    return [
        'totalUpdated' => $totalUpdated,
        'totalSkipped' => $totalSkipped,
        'totalErrors' => $totalErrors
    ];
}

// Check if script is run from command line or web
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    // Web interface
    echo "<!DOCTYPE html>\n<html>\n<head>\n<title>Language Cache Generation</title>\n</head>\n<body>\n";
    echo "<h1>Smart Language Cache Generation Tool</h1>\n";
    echo "<pre>\n";
}

// Perform cache generation
$results = generateLanguageCachesWithComparison($languageMapping);

// Calculate total time
$endTime = microtime(true);
$totalTime = $endTime - $startTime;
$minutes = floor($totalTime / 60);
$seconds = $totalTime % 60;

logMessage(sprintf("Cache generation completed in %d minutes and %.2f seconds", $minutes, $seconds));
logMessage("Summary: {$results['totalUpdated']} files updated, {$results['totalSkipped']} files skipped (no changes), {$results['totalErrors']} errors");

if (!$isCommandLine) {
    echo "</pre>\n";
    echo "<h2>Summary</h2>\n";
    echo "<p>Languages processed: " . count($languageMapping) . "</p>\n";
    echo "<p>Files updated: {$results['totalUpdated']}</p>\n";
    echo "<p>Files skipped (no changes): {$results['totalSkipped']}</p>\n";
    echo "<p>Total errors: {$results['totalErrors']}</p>\n";
    echo "<p>Total time: {$minutes} minutes and " . number_format($seconds, 2) . " seconds</p>\n";
    echo "<p><a href='test_table.php'>View Price Table</a></p>\n";
    echo "<p><a href='cache_status.php'>View Cache Status</a></p>\n";
    echo "</body>\n</html>\n";
}

// Exit with appropriate code
exit($results['totalErrors'] > 0 ? 1 : 0);

?>
