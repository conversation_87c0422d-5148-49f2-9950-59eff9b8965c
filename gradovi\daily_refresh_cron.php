<?php

/**
 * Daily Price Refresh Script
 * 
 * This script is designed to be run daily via cron job to refresh all price caches.
 * 
 * Cron job example (runs every day at 6 AM):
 * 0 6 * * * /usr/bin/php /path/to/test/daily_refresh_cron.php
 * 
 * Or on Windows Task Scheduler:
 * php.exe "E:\AO Gitlab\test\daily_refresh_cron.php"
 */

// Ensure script runs only from command line
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('This script can only be run from command line.');
}

// Set working directory to script location
chdir(__DIR__);

require_once 'inc/cene/ApiPriceClient.php';

// Configuration - All supported languages (from languageMapping in generate_all_languages.php)
$languages = [
    'engleski',
    'nemački',
    'italijanski',
    'španski',
    'francuski',
    'norveški',
    'švedski',
    'ruski',
    'grčki',
    'turski',
    'japanski',
    'kineski',
    'arapski',
    'albanski',
    'bugarski',
    'bosanski',
    'češki',
    'danski',
    'estonski',
    'hebrejski',
    'holandski',
    'hrvatski',
    'korejski',
    'mađarski',
    'makedonski',
    'poljski',
    'portugalski',
    'rumunski',
    'slovački',
    'slovenački',
    'srpski',
    'ukrajinski',
    'finski',
    'latinski',
    'znakovni',
    'bunjevački',
    'crnogorski',
    'litvanski',
    'rusinski'
];
$logFile = __DIR__ . '/logs/daily_refresh.log';
$lockFile = __DIR__ . '/logs/refresh.lock';

// Ensure log directory exists
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND | LOCK_EX);
    echo "[{$timestamp}] {$message}\n";
}

function acquireLock($lockFile) {
    if (file_exists($lockFile)) {
        $lockTime = filemtime($lockFile);
        // If lock is older than 1 hour, assume previous process died
        if (time() - $lockTime > 3600) {
            unlink($lockFile);
        } else {
            return false;
        }
    }
    
    file_put_contents($lockFile, getmypid());
    return true;
}

function releaseLock($lockFile) {
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
}

// Main execution
logMessage("Starting daily price refresh", $logFile);

// Acquire lock to prevent multiple instances
if (!acquireLock($lockFile)) {
    logMessage("Another refresh process is already running. Exiting.", $logFile);
    exit(1);
}

$totalSuccess = 0;
$totalErrors = 0;

try {
    foreach ($languages as $language) {
        logMessage("Refreshing prices for language: {$language}", $logFile);
        
        $apiClient = new ApiPriceClient();
        $result = $apiClient->refreshAllCaches($language);
        
        if ($result) {
            $successCount = count($result['refreshed']);
            $errorCount = count($result['errors']);
            
            $totalSuccess += $successCount;
            $totalErrors += $errorCount;
            
            logMessage("Language {$language}: {$successCount} cities refreshed, {$errorCount} errors", $logFile);
            
            if (!empty($result['errors'])) {
                foreach ($result['errors'] as $city => $error) {
                    logMessage("Error in {$city}: {$error}", $logFile);
                }
            }
        } else {
            logMessage("Failed to refresh prices for language: {$language}", $logFile);
            $totalErrors++;
        }
    }
    
    logMessage("Daily refresh completed. Total: {$totalSuccess} successful, {$totalErrors} errors", $logFile);
    
} catch (Exception $e) {
    logMessage("Fatal error during daily refresh: " . $e->getMessage(), $logFile);
    $totalErrors++;
} finally {
    releaseLock($lockFile);
}

// Exit with error code if there were any errors
exit($totalErrors > 0 ? 1 : 0);
?>
