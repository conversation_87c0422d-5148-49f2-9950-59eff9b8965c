<?php

class CityMapping {
    public static $cities = [
        'arandjelovac' => ['id' => 263, 'apiId' => 77, 'naziv' => 'Aranđelovac'],
        'banovo-brdo' => ['id' => 5, 'apiId' => 189, 'naziv' => 'Banovo Brdo'],
        'beograd' => ['id' => 3, 'apiId' => 105, 'naziv' => 'Beograd'],
        'bor' => ['id' => 27, 'apiId' => 167, 'naziv' => 'Bor'],
        'borca' => ['id' => 268, 'apiId' => 211, 'naziv' => 'Borča'],
        'cacak' => ['id' => 12, 'apiId' => 214, 'naziv' => 'Čačak'],
        'cuprija' => ['id' => 11, 'apiId' => 81, 'naziv' => 'Ćuprija'],
        'indjija' => ['id' => 38, 'apiId' => 213, 'naziv' => 'Inđija'],
        'jagodina' => ['id' => 1, 'apiId' => 101, 'naziv' => 'Jagodina'],
        'kragujevac' => ['id' => 7, 'apiId' => 102, 'naziv' => 'Kragujevac'],
        'kraljevo' => ['id' => 13, 'apiId' => 225, 'naziv' => 'Kraljevo'],
        'krusevac' => ['id' => 35, 'apiId' => 224, 'naziv' => 'Kruševac'],
        'leskovac' => ['id' => 9, 'apiId' => 230, 'naziv' => 'Leskovac'],
        'mladenovac' => ['id' => 6, 'apiId' => 232, 'naziv' => 'Mladenovac'],
        'nis' => ['id' => 8, 'apiId' => 99, 'naziv' => 'Niš'],
        'novi-beograd' => ['id' => 4, 'apiId' => 226, 'naziv' => 'Novi Beograd'],
        'novi-pazar' => ['id' => 25, 'apiId' => 27, 'naziv' => 'Novi Pazar'],
        'novi-sad' => ['id' => 19, 'apiId' => 196, 'naziv' => 'Novi Sad'],
        'obrenovac' => ['id' => 266, 'apiId' => 220, 'naziv' => 'Obrenovac'],
        'pancevo' => ['id' => 21, 'apiId' => 73, 'naziv' => 'Pančevo'],
        'paracin' => ['id' => 2, 'apiId' => 227, 'naziv' => 'Paraćin'],
        'pozarevac' => ['id' => 15, 'apiId' => 83, 'naziv' => 'Požarevac'],
        'smederevo' => ['id' => 16, 'apiId' => 20, 'naziv' => 'Smederevo'],
        'sombor' => ['id' => 10, 'apiId' => 234, 'naziv' => 'Sombor'],
        'subotica' => ['id' => 20, 'apiId' => 17, 'naziv' => 'Subotica'],
        'sremska-mitrovica' => ['id' => 32, 'apiId' => 199, 'naziv' => 'Sremska Mitrovica'],
        'sabac' => ['id' => 24, 'apiId' => 71, 'naziv' => 'Šabac'],
        'uzice' => ['id' => 17, 'apiId' => 141, 'naziv' => 'Užice'],
        'valjevo' => ['id' => 26, 'apiId' => 206, 'naziv' => 'Valjevo'],
        'vozdovac' => ['id' => 30, 'apiId' => 179, 'naziv' => 'Voždovac'],
        'vranje' => ['id' => 18, 'apiId' => 228, 'naziv' => 'Vranje'],
        'vrsac' => ['id' => 39, 'apiId' => 223, 'naziv' => 'Vršac'],
        'zajecar' => ['id' => 31, 'apiId' => 154, 'naziv' => 'Zaječar'],
        'zemun' => ['id' => 22, 'apiId' => 184, 'naziv' => 'Zemun'],
        'zrenjanin' => ['id' => 23, 'apiId' => 22, 'naziv' => 'Zrenjanin'],
        'ruma' => ['id' => 33, 'apiId' => 210, 'naziv' => 'Ruma'],
        'pirot' => ['id' => 261, 'apiId' => 100, 'naziv' => 'Pirot'],
        'stara-pazova' => ['id' => 262, 'apiId' => 233, 'naziv' => 'Stara Pazova'],
        'zvezdara' => ['id' => 264, 'apiId' => 183, 'naziv' => 'Zvezdara'],
        'kikinda' => ['id' => 267, 'apiId' => 86, 'naziv' => 'Kikinda']
    ];

    public static $courseTypes = [
        'grupni' => 1,
        'individualni' => 2,
        'nedefinisano' => 3,
        'grupni_ubrzani' => 4,
        'grupni_online' => 5,
        'individualni_online' => 6,
        'specijalisticki' => 7,
        'mikro_grupa' => 13,
        'grupa_4_plus' => 12
    ];

    public static function getApiIdByCity($cityName) {
        return isset(self::$cities[$cityName]['apiId']) ? self::$cities[$cityName]['apiId'] : null;
    }

    public static function getCityNameByApiId($apiId) {
        foreach (self::$cities as $key => $city) {
            if ($city['apiId'] == $apiId) {
                return $key;
            }
        }
        return null;
    }

    public static function getAllCities() {
        return self::$cities;
    }

    public static function getCourseTypeId($courseType) {
        return isset(self::$courseTypes[$courseType]) ? self::$courseTypes[$courseType] : null;
    }

    public static function getAllCourseTypes() {
        return self::$courseTypes;
    }
}

?>
